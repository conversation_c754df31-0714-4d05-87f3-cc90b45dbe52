import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Code2,
  ExternalLink,
  Star,
  GitFork,
  Code,
  Users,
  Calendar,
  Activity,
} from "lucide-react";
import { useTheme } from "../themes";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const GitHubContributions = () => {
  const { theme } = useTheme();

  const projects = [
    {
      name: "SaaS Idea Scanner",
      description:
        "AI-powered SaaS idea analysis and validation platform with comprehensive market insights.",
      url: "https://github.com/balshaer/saascan",
      language: "TypeScript",
      stars: "⭐ New",
      topics: ["react", "typescript", "ai", "saas", "analysis", "vite"],
    },
    {
      name: "Portfolio Projects",
      description:
        "Collection of web development projects showcasing modern frameworks and best practices.",
      url: "https://github.com/balshaer",
      language: "JavaScript",
      stars: "🚀 Active",
      topics: ["react", "nextjs", "nodejs", "portfolio", "web-development"],
    },
  ];

  const contributions = [
    {
      title: "Open Source Contributions",
      count: "50+",
      description: "Commits to various open source projects",
      icon: <Code className="w-6 h-6" />,
    },
    {
      title: "Public Repositories",
      count: "25+",
      description: "Projects available for the community",
      icon: <Code2 className="w-6 h-6" />,
    },
    {
      title: "Years Active",
      count: "3+",
      description: "Consistent development activity",
      icon: <Calendar className="w-6 h-6" />,
    },
    {
      title: "Technologies",
      count: "15+",
      description: "Different programming languages and frameworks",
      icon: <Activity className="w-6 h-6" />,
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <div
      className="min-h-screen flex flex-col"
      style={{ backgroundColor: theme.colors.background.primary }}
    >
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-12">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-6xl mx-auto space-y-12"
        >
          {/* Hero Section */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <div className="flex justify-center mb-6">
              <div
                className="w-20 h-20 rounded-full flex items-center justify-center"
                style={{ backgroundColor: theme.colors.primary[100] }}
              >
                <Code2
                  className="w-10 h-10"
                  style={{ color: theme.colors.primary[600] }}
                />
              </div>
            </div>
            <h1
              className="text-4xl md:text-5xl font-bold"
              style={{ color: theme.colors.text.primary }}
            >
              GitHub Contributions
            </h1>
            <p
              className="text-xl max-w-3xl mx-auto leading-relaxed"
              style={{ color: theme.colors.text.secondary }}
            >
              Explore my open source contributions, projects, and development
              activity on GitHub. Building innovative solutions and contributing
              to the developer community.
            </p>
            <Button
              onClick={() =>
                window.open(
                  "https://github.com/balshaer",
                  "_blank",
                  "noopener noreferrer"
                )
              }
              className="inline-flex items-center gap-2 px-6 py-3"
              style={{
                backgroundColor: theme.colors.primary[500],
                color: theme.colors.text.inverse,
              }}
            >
              <Code2 className="w-4 h-4" />
              View GitHub Profile
              <ExternalLink className="w-3 h-3" />
            </Button>
          </motion.div>

          {/* Stats Grid */}
          <motion.div variants={itemVariants}>
            <h2
              className="text-3xl font-bold text-center mb-8"
              style={{ color: theme.colors.text.primary }}
            >
              Contribution Stats
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {contributions.map((stat, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card
                    className="text-center border-0 shadow-lg"
                    style={{ backgroundColor: theme.colors.background.card }}
                  >
                    <CardContent className="pt-6">
                      <div
                        className="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4"
                        style={{ backgroundColor: theme.colors.accent[100] }}
                      >
                        <div style={{ color: theme.colors.accent[600] }}>
                          {stat.icon}
                        </div>
                      </div>
                      <div
                        className="text-3xl font-bold mb-2"
                        style={{ color: theme.colors.text.primary }}
                      >
                        {stat.count}
                      </div>
                      <div
                        className="font-semibold mb-1"
                        style={{ color: theme.colors.text.primary }}
                      >
                        {stat.title}
                      </div>
                      <p
                        className="text-sm"
                        style={{ color: theme.colors.text.secondary }}
                      >
                        {stat.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Featured Projects */}
          <motion.div variants={itemVariants}>
            <h2
              className="text-3xl font-bold text-center mb-8"
              style={{ color: theme.colors.text.primary }}
            >
              Featured Projects
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              {projects.map((project, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card
                    className="h-full border-0 shadow-lg"
                    style={{ backgroundColor: theme.colors.background.card }}
                  >
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle
                            className="text-xl mb-2"
                            style={{ color: theme.colors.text.primary }}
                          >
                            {project.name}
                          </CardTitle>
                          <div className="flex items-center gap-2 mb-3">
                            <Badge
                              variant="outline"
                              style={{
                                borderColor: theme.colors.primary[300],
                                color: theme.colors.primary[600],
                              }}
                            >
                              {project.language}
                            </Badge>
                            <span
                              className="text-sm"
                              style={{ color: theme.colors.text.secondary }}
                            >
                              {project.stars}
                            </span>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            window.open(
                              project.url,
                              "_blank",
                              "noopener noreferrer"
                            )
                          }
                          className="flex items-center gap-1"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p
                        className="text-sm leading-relaxed mb-4"
                        style={{ color: theme.colors.text.secondary }}
                      >
                        {project.description}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {project.topics.map((topic, topicIndex) => (
                          <Badge
                            key={topicIndex}
                            variant="secondary"
                            className="text-xs"
                            style={{
                              backgroundColor:
                                theme.colors.background.secondary,
                              color: theme.colors.text.secondary,
                            }}
                          >
                            {topic}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* GitHub Activity Placeholder */}
          <motion.div variants={itemVariants}>
            <Card
              className="border-0 shadow-lg"
              style={{ backgroundColor: theme.colors.background.card }}
            >
              <CardHeader>
                <CardTitle
                  className="text-2xl text-center"
                  style={{ color: theme.colors.text.primary }}
                >
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div
                  className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                  style={{ backgroundColor: theme.colors.status.info.bg }}
                >
                  <Activity
                    className="w-8 h-8"
                    style={{ color: theme.colors.status.info.text }}
                  />
                </div>
                <p
                  className="text-lg mb-4"
                  style={{ color: theme.colors.text.secondary }}
                >
                  Visit my GitHub profile to see the latest commits, pull
                  requests, and project updates.
                </p>
                <Button
                  onClick={() =>
                    window.open(
                      "https://github.com/balshaer",
                      "_blank",
                      "noopener noreferrer"
                    )
                  }
                  variant="outline"
                  className="inline-flex items-center gap-2"
                >
                  <Code2 className="w-4 h-4" />
                  View Live Activity
                  <ExternalLink className="w-3 h-3" />
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </main>

      <Footer />
    </div>
  );
};

export default GitHubContributions;
