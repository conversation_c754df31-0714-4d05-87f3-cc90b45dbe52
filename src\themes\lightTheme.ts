import { Theme } from "./types";

export const lightTheme: Theme = {
  name: "light",
  displayName: "Light Mode",
  colors: {
    // Core semantic colors
    background: "#ffffff", // Main background
    surface: "#ffffff", // Card/surface backgrounds
    headline: "#111827", // Primary text/headings
    paragraph: "#374151", // Body text
    muted: "#6b7280", // Secondary/muted text
    border: "#e5e7eb", // Borders and dividers
    accent: "#3b82f6", // Primary accent/brand color
    inverse: "#ffffff", // Text on dark backgrounds

    // Interactive states
    hover: "#f9fafb", // Hover state background
    focus: "#3b82f6", // Focus state color

    // Status colors (simplified)
    success: "#22c55e", // Success color
    warning: "#f59e0b", // Warning color
    error: "#ef4444", // Error color
    info: "#3b82f6", // Info color

    // Legacy support (for gradual migration)
    primary: {
      50: "#eff6ff",
      100: "#dbeafe",
      200: "#bfdbfe",
      300: "#93c5fd",
      400: "#60a5fa",
      500: "#3b82f6",
      600: "#2563eb",
      700: "#1d4ed8",
      800: "#1e40af",
      900: "#1e3a8a",
      950: "#172554",
    },

    secondary: {
      50: "#f9fafb",
      100: "#f3f4f6",
      200: "#e5e7eb",
      300: "#d1d5db",
      400: "#9ca3af",
      500: "#6b7280",
      600: "#4b5563",
      700: "#374151",
      800: "#1f2937",
      900: "#111827",
      950: "#030712",
    },
  },
};
