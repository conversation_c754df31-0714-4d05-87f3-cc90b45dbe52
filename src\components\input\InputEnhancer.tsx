import { enhanceSaasIdea } from "@/lib/aiEnhancer";
import { useToast } from "@/hooks/use-toast";

interface InputEnhancerProps {
  setInput: (value: string) => void;
}

export const useInputEnhancer = ({ setInput }: InputEnhancerProps) => {
  const { toast } = useToast();

  const handleEnhance = async (value: string): Promise<void> => {
    if (!value.trim()) return;

    try {
      const result = await enhanceSaasIdea(value);

      if (result.success) {
        setInput(result.enhanced);
        toast({
          title: "Idea Enhanced!",
          description: "Your SaaS idea has been enhanced with AI suggestions.",
        });
      } else {
        setInput(result.enhanced);
        toast({
          title: "Idea Enhanced",
          description:
            "Enhanced using offline suggestions. For better results, check your API configuration.",
          variant: "default",
        });
      }
    } catch (error) {
      console.error("Enhancement failed:", error);
      toast({
        title: "Enhancement Failed",
        description: "Unable to enhance your idea. Please try again later.",
        variant: "destructive",
      });
    }
  };

  return { handleEnhance };
};
