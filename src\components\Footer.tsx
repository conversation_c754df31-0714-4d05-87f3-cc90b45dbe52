import React from "react";
import { Github } from "lucide-react";
import { useTheme } from "../themes";

const Footer = () => {
  const { theme } = useTheme();

  return (
    <div className="px-4 py-12 mx-auto  sm:px-6 lg:py-16 lg:px-8 w-full container">
      <div
        className="pt-8 mt-8 border-t md:flex md:items-center md:justify-between"
        style={{ borderColor: theme.colors.border }}
      >
        <p className="text-base" style={{ color: theme.colors.muted }}>
          SaaS Idea Scanner, Built by{" "}
          <a
            className="hover:underline"
            style={{ color: theme.colors.accent }}
            href="https://github.com/balshaer"
            target="_blank"
            rel="noopener noreferrer"
          >
            Baraa Alshaer
          </a>
        </p>
        <div className="flex mt-8 space-x-6 md:mt-0">
          <a
            href="https://www.jsdelivr.com/package/npm/theme-toggles"
            className="hover:underline"
            style={{ color: theme.colors.muted }}
            target="_blank"
            rel="noopener noreferrer"
          >
            <span className="sr-only">balshaer</span>b<b>alshaer</b>
          </a>
          <a
            href="https://github.com/balshaer/saascan"
            className="hover:opacity-75"
            style={{ color: theme.colors.muted }}
            target="_blank"
            rel="noopener noreferrer"
          >
            <span className="sr-only">Github</span>
            <Github className="w-6 h-6" />
          </a>
        </div>
      </div>
    </div>
  );
};

export default Footer;
