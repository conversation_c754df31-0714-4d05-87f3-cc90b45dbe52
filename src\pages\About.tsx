import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Target,
  Brain,
  Zap,
  Users,
  Shield,
  Sparkles,
  Code,
  ExternalLink,
} from "lucide-react";
import { useTheme } from "../themes";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const About = () => {
  const { theme } = useTheme();

  const features = [
    {
      icon: <Brain className="w-6 h-6" />,
      title: "AI-Powered Analysis",
      description:
        "Advanced AI algorithms analyze your SaaS ideas for market potential, technical feasibility, and competitive landscape.",
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "Market Validation",
      description:
        "Get insights into target audience, market size, and competitive positioning to validate your business concept.",
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Technical Feasibility",
      description:
        "Understand development complexity, tech stack recommendations, and resource requirements for your idea.",
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "User Experience Focus",
      description:
        "Receive UX recommendations, user journey analysis, and accessibility considerations for your product.",
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Risk Assessment",
      description:
        "Identify potential risks, challenges, and mitigation strategies for your SaaS venture.",
    },
    {
      icon: <Sparkles className="w-6 h-6" />,
      title: "Strategic Recommendations",
      description:
        "Get actionable insights and strategic guidance to turn your idea into a successful SaaS business.",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
    },
  };

  return (
    <div
      className="min-h-screen flex flex-col"
      style={{ backgroundColor: theme.colors.background }}
    >
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-12">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-4xl mx-auto space-y-12"
        >
          {/* Hero Section */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h1
              className="text-4xl md:text-5xl font-bold"
              style={{ color: theme.colors.headline }}
            >
              About SaaS Idea Scanner
            </h1>
            <p
              className="text-xl max-w-3xl mx-auto leading-relaxed"
              style={{ color: theme.colors.paragraph }}
            >
              Transform your SaaS ideas into actionable business insights with
              our AI-powered analysis platform. Get comprehensive market
              validation, technical feasibility assessment, and strategic
              recommendations.
            </p>
            <div className="flex justify-center gap-4 flex-wrap">
              <Badge
                variant="outline"
                className="px-4 py-2 text-sm"
                style={{
                  borderColor: theme.colors.accent,
                  color: theme.colors.accent,
                }}
              >
                AI-Powered
              </Badge>
              <Badge
                variant="outline"
                className="px-4 py-2 text-sm"
                style={{
                  borderColor: theme.colors.info,
                  color: theme.colors.info,
                }}
              >
                Market Validation
              </Badge>
              <Badge
                variant="outline"
                className="px-4 py-2 text-sm"
                style={{
                  borderColor: theme.colors.success,
                  color: theme.colors.success,
                }}
              >
                Strategic Insights
              </Badge>
            </div>
          </motion.div>

          {/* Features Grid */}
          <motion.div variants={itemVariants}>
            <h2
              className="text-3xl font-bold text-center mb-8"
              style={{ color: theme.colors.headline }}
            >
              Key Features
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card
                    className="h-full border-0 shadow-lg"
                    style={{ backgroundColor: theme.colors.surface }}
                  >
                    <CardHeader>
                      <div
                        className="w-12 h-12 rounded-lg flex items-center justify-center mb-4"
                        style={{ backgroundColor: theme.colors.primary[100] }}
                      >
                        <div style={{ color: theme.colors.primary[600] }}>
                          {feature.icon}
                        </div>
                      </div>
                      <CardTitle
                        className="text-lg"
                        style={{ color: theme.colors.headline }}
                      >
                        {feature.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p
                        className="text-sm leading-relaxed"
                        style={{ color: theme.colors.paragraph }}
                      >
                        {feature.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Mission Statement */}
          <motion.div variants={itemVariants}>
            <Card
              className="border-0 shadow-lg"
              style={{ backgroundColor: theme.colors.surface }}
            >
              <CardHeader>
                <CardTitle
                  className="text-2xl text-center"
                  style={{ color: theme.colors.headline }}
                >
                  Our Mission
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p
                  className="text-lg leading-relaxed max-w-2xl mx-auto"
                  style={{ color: theme.colors.paragraph }}
                >
                  We believe every great SaaS idea deserves a chance to succeed.
                  Our platform democratizes access to professional-grade
                  business analysis, helping entrepreneurs and innovators make
                  informed decisions about their ventures before investing time
                  and resources.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* CTA Section */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2
              className="text-2xl font-bold"
              style={{ color: theme.colors.headline }}
            >
              Ready to Analyze Your SaaS Idea?
            </h2>
            <div className="flex justify-center gap-4">
              <motion.a
                href="/"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors"
                style={{
                  backgroundColor: theme.colors.accent,
                  color: theme.colors.inverse,
                }}
              >
                <Sparkles className="w-4 h-4" />
                Start Analysis
              </motion.a>
              <motion.a
                href="https://github.com/balshaer/saascan"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center gap-2 px-6 py-3 rounded-lg font-semibold border transition-colors"
                style={{
                  borderColor: theme.colors.border,
                  color: theme.colors.headline,
                }}
              >
                <Code className="w-4 h-4" />
                View Source
                <ExternalLink className="w-3 h-3" />
              </motion.a>
            </div>
          </motion.div>
        </motion.div>
      </main>

      <Footer />
    </div>
  );
};

export default About;
