import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Sparkles } from "lucide-react";
import { useTheme } from "../../themes";

interface InputContainerProps {
  children: React.ReactNode;
  title?: string;
}

const InputContainer: React.FC<InputContainerProps> = ({
  children,
  title = "Analyze Your SaaS Idea",
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const { theme } = useTheme();

  const containerVariants = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
  };

  const headerVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
  };

  const sparklesVariants = {
    idle: { rotate: 0 },
    focused: { rotate: 360 },
  };

  return (
    <motion.div
      className="grid grid-cols-1 gap-8 w-full mx-auto"
      variants={containerVariants}
      initial="initial"
      animate="animate"
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <Card
        className="shadow-xl border-0 backdrop-blur-md overflow-hidden"
        style={{ backgroundColor: `${theme.colors.surface}CC` }}
      >
        <motion.div
          animate={{
            background: isFocused
              ? `linear-gradient(135deg, ${theme.colors.accent}0D, ${theme.colors.info}0D)`
              : "transparent",
          }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0 pointer-events-none"
        />

        <CardHeader className="pb-4 relative z-10">
          <motion.div
            variants={headerVariants}
            initial="initial"
            animate="animate"
            transition={{ delay: 0.2 }}
          >
            <CardTitle
              className="flex items-center mx-auto gap-3 text-2xl"
              style={{ color: theme.colors.headline }}
            >
              <motion.div
                variants={sparklesVariants}
                animate={isFocused ? "focused" : "idle"}
                transition={{ duration: 0.5 }}
              >
                <Sparkles
                  className="w-6 h-6"
                  style={{ color: theme.colors.accent }}
                />
              </motion.div>
              {title}
            </CardTitle>
          </motion.div>
        </CardHeader>

        <CardContent className="space-y-6 relative z-10">
          {React.Children.map(children, (child) => {
            if (React.isValidElement(child)) {
              return React.cloneElement(child, {
                onFocus: () => setIsFocused(true),
                onBlur: () => setIsFocused(false),
              } as any);
            }
            return child;
          })}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default InputContainer;
