import { Theme } from "./types";

export const darkTheme: Theme = {
  name: "dark",
  displayName: "Dark Mode",
  colors: {
    // Core semantic colors
    background: "#0f172a", // Main background
    surface: "#1e293b", // Card/surface backgrounds
    headline: "#f8fafc", // Primary text/headings
    paragraph: "#e2e8f0", // Body text
    muted: "#94a3b8", // Secondary/muted text
    border: "#334155", // Borders and dividers
    accent: "#60a5fa", // Primary accent/brand color
    inverse: "#0f172a", // Text on light backgrounds

    // Interactive states
    hover: "#334155", // Hover state background
    focus: "#3b82f6", // Focus state color

    // Status colors (simplified)
    success: "#22c55e", // Success color
    warning: "#f59e0b", // Warning color
    error: "#ef4444", // Error color
    info: "#60a5fa", // Info color

    // Legacy support (for gradual migration)
    primary: {
      50: "#172554",
      100: "#1e3a8a",
      200: "#1e40af",
      300: "#1d4ed8",
      400: "#2563eb",
      500: "#3b82f6",
      600: "#60a5fa",
      700: "#93c5fd",
      800: "#bfdbfe",
      900: "#dbeafe",
      950: "#eff6ff",
    },

    secondary: {
      50: "#030712",
      100: "#111827",
      200: "#1f2937",
      300: "#374151",
      400: "#4b5563",
      500: "#6b7280",
      600: "#9ca3af",
      700: "#d1d5db",
      800: "#e5e7eb",
      900: "#f3f4f6",
      950: "#f9fafb",
    },
  },
};
