import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { Theme, ThemeMode, ThemeContextType } from "../themes/types";
import { lightTheme } from "../themes/lightTheme";
import { darkTheme } from "../themes/darkTheme";

// Create the theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme storage key
const THEME_STORAGE_KEY = "mindful-ux-analyzer-theme";

// Theme provider props
interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: ThemeMode;
}

// Get the theme object based on mode
const getThemeByMode = (mode: ThemeMode): Theme => {
  switch (mode) {
    case "dark":
      return darkTheme;
    case "light":
    default:
      return lightTheme;
  }
};

// Get initial theme from localStorage or system preference
const getInitialTheme = (defaultTheme?: ThemeMode): ThemeMode => {
  // Check if we have a stored preference
  try {
    const stored = localStorage.getItem(THEME_STORAGE_KEY);
    if (stored && (stored === "light" || stored === "dark")) {
      return stored as ThemeMode;
    }
  } catch (error) {
    console.warn("Failed to read theme from localStorage:", error);
  }

  // Use provided default
  if (defaultTheme) {
    return defaultTheme;
  }

  // Check system preference
  if (typeof window !== "undefined" && window.matchMedia) {
    const prefersDark = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;
    return prefersDark ? "dark" : "light";
  }

  // Fallback to light theme
  return "light";
};

// Apply theme CSS variables to document root
const applyThemeToDocument = (theme: Theme) => {
  if (typeof document === "undefined") return;

  const root = document.documentElement;
  const { colors } = theme;

  // Primary colors
  root.style.setProperty("--theme-primary-50", colors.primary[50]);
  root.style.setProperty("--theme-primary-100", colors.primary[100]);
  root.style.setProperty("--theme-primary-200", colors.primary[200]);
  root.style.setProperty("--theme-primary-300", colors.primary[300]);
  root.style.setProperty("--theme-primary-400", colors.primary[400]);
  root.style.setProperty("--theme-primary-500", colors.primary[500]);
  root.style.setProperty("--theme-primary-600", colors.primary[600]);
  root.style.setProperty("--theme-primary-700", colors.primary[700]);
  root.style.setProperty("--theme-primary-800", colors.primary[800]);
  root.style.setProperty("--theme-primary-900", colors.primary[900]);
  root.style.setProperty("--theme-primary-950", colors.primary[950]);

  // Secondary colors
  root.style.setProperty("--theme-secondary-50", colors.secondary[50]);
  root.style.setProperty("--theme-secondary-100", colors.secondary[100]);
  root.style.setProperty("--theme-secondary-200", colors.secondary[200]);
  root.style.setProperty("--theme-secondary-300", colors.secondary[300]);
  root.style.setProperty("--theme-secondary-400", colors.secondary[400]);
  root.style.setProperty("--theme-secondary-500", colors.secondary[500]);
  root.style.setProperty("--theme-secondary-600", colors.secondary[600]);
  root.style.setProperty("--theme-secondary-700", colors.secondary[700]);
  root.style.setProperty("--theme-secondary-800", colors.secondary[800]);
  root.style.setProperty("--theme-secondary-900", colors.secondary[900]);
  root.style.setProperty("--theme-secondary-950", colors.secondary[950]);

  // Background colors
  root.style.setProperty("--theme-bg-primary", colors.background.primary);
  root.style.setProperty("--theme-bg-secondary", colors.background.secondary);
  root.style.setProperty("--theme-bg-tertiary", colors.background.tertiary);
  root.style.setProperty("--theme-bg-card", colors.background.card);
  root.style.setProperty("--theme-bg-sidebar", colors.background.sidebar);
  root.style.setProperty("--theme-bg-modal", colors.background.modal);
  root.style.setProperty("--theme-bg-overlay", colors.background.overlay);

  // Text colors
  root.style.setProperty("--theme-text-primary", colors.text.primary);
  root.style.setProperty("--theme-text-secondary", colors.text.secondary);
  root.style.setProperty("--theme-text-tertiary", colors.text.tertiary);
  root.style.setProperty("--theme-text-muted", colors.text.muted);
  root.style.setProperty("--theme-text-inverse", colors.text.inverse);
  root.style.setProperty("--theme-text-link", colors.text.link);
  root.style.setProperty("--theme-text-link-hover", colors.text.linkHover);

  // Border colors
  root.style.setProperty("--theme-border-primary", colors.border.primary);
  root.style.setProperty("--theme-border-secondary", colors.border.secondary);
  root.style.setProperty("--theme-border-tertiary", colors.border.tertiary);
  root.style.setProperty("--theme-border-focus", colors.border.focus);
  root.style.setProperty("--theme-border-hover", colors.border.hover);

  // Table colors
  root.style.setProperty("--theme-table-header-bg", colors.table.header.bg);
  root.style.setProperty("--theme-table-header-text", colors.table.header.text);
  root.style.setProperty(
    "--theme-table-header-border",
    colors.table.header.border
  );
  root.style.setProperty("--theme-table-row-bg", colors.table.row.bg);
  root.style.setProperty(
    "--theme-table-row-bg-hover",
    colors.table.row.bgHover
  );
  root.style.setProperty(
    "--theme-table-row-bg-selected",
    colors.table.row.bgSelected
  );
  root.style.setProperty("--theme-table-row-text", colors.table.row.text);
  root.style.setProperty("--theme-table-row-border", colors.table.row.border);

  // Status colors
  root.style.setProperty("--theme-success-bg", colors.status.success.bg);
  root.style.setProperty("--theme-success-text", colors.status.success.text);
  root.style.setProperty(
    "--theme-success-border",
    colors.status.success.border
  );
  root.style.setProperty("--theme-warning-bg", colors.status.warning.bg);
  root.style.setProperty("--theme-warning-text", colors.status.warning.text);
  root.style.setProperty(
    "--theme-warning-border",
    colors.status.warning.border
  );
  root.style.setProperty("--theme-error-bg", colors.status.error.bg);
  root.style.setProperty("--theme-error-text", colors.status.error.text);
  root.style.setProperty("--theme-error-border", colors.status.error.border);
  root.style.setProperty("--theme-info-bg", colors.status.info.bg);
  root.style.setProperty("--theme-info-text", colors.status.info.text);
  root.style.setProperty("--theme-info-border", colors.status.info.border);

  // Risk colors
  root.style.setProperty("--theme-risk-low-bg", colors.risk.low.bg);
  root.style.setProperty("--theme-risk-low-text", colors.risk.low.text);
  root.style.setProperty("--theme-risk-medium-bg", colors.risk.medium.bg);
  root.style.setProperty("--theme-risk-medium-text", colors.risk.medium.text);
  root.style.setProperty("--theme-risk-high-bg", colors.risk.high.bg);
  root.style.setProperty("--theme-risk-high-text", colors.risk.high.text);

  // Score colors
  root.style.setProperty(
    "--theme-score-excellent-bg",
    colors.score.excellent.bg
  );
  root.style.setProperty(
    "--theme-score-excellent-text",
    colors.score.excellent.text
  );
  root.style.setProperty("--theme-score-good-bg", colors.score.good.bg);
  root.style.setProperty("--theme-score-good-text", colors.score.good.text);
  root.style.setProperty("--theme-score-average-bg", colors.score.average.bg);
  root.style.setProperty(
    "--theme-score-average-text",
    colors.score.average.text
  );
  root.style.setProperty("--theme-score-poor-bg", colors.score.poor.bg);
  root.style.setProperty("--theme-score-poor-text", colors.score.poor.text);

  // Add theme class to body for additional styling
  document.body.className = document.body.className.replace(/theme-\w+/g, "");
  document.body.classList.add(`theme-${theme.name}`);
};

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme,
}) => {
  const [themeMode, setThemeMode] = useState<ThemeMode>(() =>
    getInitialTheme(defaultTheme)
  );

  const theme = getThemeByMode(themeMode);

  // Apply theme to document when theme changes
  useEffect(() => {
    applyThemeToDocument(theme);
  }, [theme]);

  // Save theme preference to localStorage
  useEffect(() => {
    try {
      localStorage.setItem(THEME_STORAGE_KEY, themeMode);
    } catch (error) {
      console.warn("Failed to save theme to localStorage:", error);
    }
  }, [themeMode]);

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === "undefined" || !window.matchMedia) return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const handleChange = (e: MediaQueryListEvent) => {
      // Only auto-switch if no manual preference is stored
      try {
        const stored = localStorage.getItem(THEME_STORAGE_KEY);
        if (!stored) {
          setThemeMode(e.matches ? "dark" : "light");
        }
      } catch (error) {
        // If localStorage fails, still respond to system changes
        setThemeMode(e.matches ? "dark" : "light");
      }
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  const toggleTheme = () => {
    setThemeMode((prev) => (prev === "light" ? "dark" : "light"));
  };

  const setTheme = (mode: ThemeMode) => {
    setThemeMode(mode);
  };

  const contextValue: ThemeContextType = {
    theme,
    themeMode,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};

export default ThemeContext;
