import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { TextShimmer } from "@/components/ui/text-shimmer";
import { useTheme } from "../../themes";

interface AnalysisButtonProps {
  onClick: () => void;
  isAnalyzing: boolean;
  isEnhancing: boolean;
  input: string;
  disabled?: boolean;
}

const AnalysisButton: React.FC<AnalysisButtonProps> = ({
  onClick,
  isAnalyzing,
  isEnhancing,
  input,
  disabled = false,
}) => {
  const { theme } = useTheme();

  const isDisabled =
    disabled ||
    isAnalyzing ||
    isEnhancing ||
    !input.trim() ||
    input.length < 20;

  const buttonVariants = {
    hover: { scale: 1.05 },
    tap: { scale: 0.95 },
  };

  return (
    <div className="flex justify-center">
      <motion.div
        variants={buttonVariants}
        whileHover="hover"
        whileTap="tap"
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        <Button
          onClick={onClick}
          disabled={isDisabled}
          size="lg"
          className="px-12 py-4 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50"
          style={{
            background: `linear-gradient(135deg, ${theme.colors.accent}, ${theme.colors.info})`,
            color: theme.colors.inverse,
          }}
          onMouseEnter={(e) => {
            if (!isDisabled) {
              e.currentTarget.style.background = `linear-gradient(135deg, ${theme.colors.primary[600]}, ${theme.colors.secondary[600]})`;
            }
          }}
          onMouseLeave={(e) => {
            if (!isDisabled) {
              e.currentTarget.style.background = `linear-gradient(135deg, ${theme.colors.accent}, ${theme.colors.info})`;
            }
          }}
        >
          <AnimatePresence mode="wait">
            {isAnalyzing ? (
              <motion.div
                key="analyzing"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex items-center gap-3"
              >
                <TextShimmer className="font-mono text-sm" duration={1}>
                  Analyzing idea...
                </TextShimmer>
              </motion.div>
            ) : isEnhancing ? (
              <motion.div
                key="enhancing"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex items-center gap-3"
              >
                <TextShimmer className="font-mono text-sm" duration={1}>
                  Enhancing idea...
                </TextShimmer>
              </motion.div>
            ) : (
              <motion.div
                key="analyze"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex items-center gap-3"
              >
                <span className="font-semibold text-lg">Analyze SaaS Idea</span>
                <motion.div
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <ArrowRight className="w-5 h-5" />
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </Button>
      </motion.div>
    </div>
  );
};

export default AnalysisButton;
