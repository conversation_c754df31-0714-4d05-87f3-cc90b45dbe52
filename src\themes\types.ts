// Theme type definitions for the mindful-ux-analyzer application

export interface ThemeColors {
  // Core semantic colors
  background: string; // Main background color
  surface: string; // Card/surface backgrounds
  headline: string; // Primary text/headings
  paragraph: string; // Body text
  muted: string; // Secondary/muted text
  border: string; // Borders and dividers
  accent: string; // Primary accent/brand color
  inverse: string; // Text on dark backgrounds

  // Interactive states
  hover: string; // Hover state background
  focus: string; // Focus state color

  // Status colors (simplified)
  success: string; // Success color
  warning: string; // Warning color
  error: string; // Error color
  info: string; // Info color

  // Legacy support (for gradual migration)
  primary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };

  secondary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };
}

export interface Theme {
  name: string;
  displayName: string;
  colors: ThemeColors;
}

export type ThemeMode = "light" | "dark";

export interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  toggleTheme: () => void;
  setTheme: (mode: ThemeMode) => void;
}
