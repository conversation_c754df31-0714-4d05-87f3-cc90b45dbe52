import React from 'react';
import { Badge } from '@/components/ui/badge';
import { useTheme } from '../../themes';

interface FinancialData {
  pricing_model?: string;
  arpu_range?: string;
  ltv_cac_ratio?: string;
  churn_rate?: string;
  gross_margin?: string;
}

interface FinancialCellProps {
  financials?: FinancialData;
}

const FinancialCell: React.FC<FinancialCellProps> = ({ financials }) => {
  const { theme } = useTheme();

  if (!financials) {
    return (
      <td className="px-6 py-4">
        <div className="text-xs text-gray-500">No financial data</div>
      </td>
    );
  }

  return (
    <td className="px-6 py-4">
      <div className="space-y-2 max-w-xs">
        {financials.pricing_model && (
          <div>
            <span className="text-xs font-medium" style={{ color: theme.colors.text.secondary }}>
              Pricing:
            </span>
            <Badge variant="outline">
              {financials.pricing_model}
            </Badge>
          </div>
        )}
        {financials.arpu_range && (
          <div>
            <span className="text-xs font-medium" style={{ color: theme.colors.text.secondary }}>
              ARPU:
            </span>
            <p className="text-xs" style={{ color: theme.colors.text.tertiary }}>
              {financials.arpu_range}
            </p>
          </div>
        )}
        {financials.ltv_cac_ratio && (
          <div>
            <span className="text-xs font-medium" style={{ color: theme.colors.text.secondary }}>
              LTV:CAC:
            </span>
            <p className="text-xs" style={{ color: theme.colors.text.tertiary }}>
              {financials.ltv_cac_ratio}
            </p>
          </div>
        )}
        {financials.churn_rate && (
          <div>
            <span className="text-xs font-medium" style={{ color: theme.colors.text.secondary }}>
              Churn:
            </span>
            <p className="text-xs" style={{ color: theme.colors.text.tertiary }}>
              {financials.churn_rate}
            </p>
          </div>
        )}
        {financials.gross_margin && (
          <div>
            <span className="text-xs font-medium" style={{ color: theme.colors.text.secondary }}>
              Margin:
            </span>
            <p className="text-xs" style={{ color: theme.colors.text.tertiary }}>
              {financials.gross_margin}
            </p>
          </div>
        )}
      </div>
    </td>
  );
};

export default FinancialCell;
