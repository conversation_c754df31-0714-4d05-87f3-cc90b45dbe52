import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { EnhancedTextarea } from "@/components/ui/textarea";
import InputValidation from "@/components/analysis/InputValidation";
import { useTheme } from "../../themes";

interface IdeaInputFormProps {
  input: string;
  setInput: (value: string) => void;
  isAnalyzing: boolean;
  isEnhancing: boolean;
  onEnhance: (value: string) => Promise<void>;
  onFocus?: () => void;
  onBlur?: () => void;
}

const IdeaInputForm: React.FC<IdeaInputFormProps> = ({
  input,
  setInput,
  isAnalyzing,
  isEnhancing,
  onEnhance,
  onFocus,
  onBlur,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const { theme } = useTheme();

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleIdeaGenerated = (idea: string) => {
    console.log("New idea generated:", idea);
  };

  return (
    <div className="space-y-3">
      <motion.div
        animate={{
          height: isFocused ? "200px" : "120px",
          scale: isFocused ? 1.02 : 1,
        }}
        transition={{
          duration: 0.4,
          ease: [0.4, 0, 0.2, 1],
        }}
        className="overflow-hidden"
      >
        <EnhancedTextarea
          placeholder="Describe your SaaS idea in detail... What problem does it solve? Who is your target audience? What makes it unique?"
          value={input}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
            setInput(e.target.value)
          }
          onFocus={handleFocus}
          onBlur={handleBlur}
          onEnhance={onEnhance}
          isEnhancing={isEnhancing}
          showEnhanceButton={!isAnalyzing}
          enableSmartSuggestions={true}
          enableUndoFunctionality={true}
          enableIdeaGenerator={true}
          autoResize={true}
          onIdeaGenerated={handleIdeaGenerated}
          className="h-full text-base border-2 transition-all duration-300 backdrop-blur-sm"
          style={{
            borderColor: isFocused ? theme.colors.accent : theme.colors.border,
            backgroundColor: `${theme.colors.background}80`,
          }}
          disabled={isAnalyzing || isEnhancing}
        />
      </motion.div>

      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <InputValidation input={input} isAnalyzing={isAnalyzing} />
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default IdeaInputForm;
