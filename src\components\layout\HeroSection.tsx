import React from "react";
import { useTheme } from "../../themes";
import { TextEffect } from "../ui/text-effect";

const HeroSection = () => {
  const { theme } = useTheme();

  return (
    <div className="pt-12 text-center">
      <h1
        className="text-4xl font-extrabold tracking-tight 2xs:text-5xl md:text-6xl lg:text-7xl"
        style={{ color: theme.colors.headline }}
      >
        <span className="block pb-1 sm:pb-2 md:pb-3 bg-gradient-to-r from-blue-600 to-[#3eadcf] text-transparent bg-clip-text">
          SaaS Idea Scanner
        </span>
        <span className="block">was never easier</span>
      </h1>

      <TextEffect
        per="char"
        className="max-w-md mx-auto mt-5 text-base sm:text-lg md:mt-7 md:text-xl md:max-w-3xl"
        style={{ color: theme.colors.paragraph }}
        preset="fade"
      >
        Get comprehensive analysis of your SaaS ideas with AI-powered insights,
        market validation, and strategic recommendations.
      </TextEffect>
    </div>
  );
};

export default HeroSection;
