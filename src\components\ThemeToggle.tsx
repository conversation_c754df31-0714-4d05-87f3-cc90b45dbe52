import React from "react";
import { motion } from "framer-motion";
import "@theme-toggles/react/css/Expand.css";
import { Expand } from "@theme-toggles/react";
import { useTheme } from "../themes";
import { Button } from "./ui/button";

interface ThemeToggleProps {
  className?: string;
  size?: "sm" | "lg" | "default" | "icon";
  variant?: "default" | "outline" | "ghost";
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = "",
  size = "default",
  variant = "ghost",
}) => {
  const { themeMode, toggleTheme } = useTheme();
  const isDark = themeMode === "dark";

  const buttonVariants = {
    hover: { scale: 1.05 },
    tap: { scale: 0.95 },
  };

  return (
    <motion.div
      variants={buttonVariants}
      whileHover="hover"
      whileTap="tap"
      className={className}
    >
      <Button
        onClick={toggleTheme}
        variant={variant}
        size={size}
        className="relative overflow-hidden flex items-center justify-center"
        aria-label={`Switch to ${isDark ? "light" : "dark"} mode`}
        title={`Switch to ${isDark ? "light" : "dark"} mode`}
      >
        <Expand
          toggled={isDark}
          duration={750}
          placeholder=""
          onPointerEnterCapture={() => {}}
          onPointerLeaveCapture={() => {}}
        />
        <span className="sr-only">
          {isDark ? "Switch to light mode" : "Switch to dark mode"}
        </span>
      </Button>
    </motion.div>
  );
};

export default ThemeToggle;
