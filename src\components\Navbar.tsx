import React from "react";
import { Home, History, Code, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link, useLocation } from "react-router-dom";
import Logo from "./Logo";
import ThemeToggle from "./ThemeToggle";
import { useTheme } from "../themes";

const Navbar = () => {
  const location = useLocation();
  const { theme } = useTheme();

  const navItems = [
    { path: "/", label: "Home", icon: <Home className="w-4 h-4" /> },
    { path: "/about", label: "About", icon: <Info className="w-4 h-4" /> },
    { path: "/github", label: "GitHub", icon: <Code className="w-4 h-4" /> },
    {
      path: "/history",
      label: "History",
      icon: <History className="w-4 h-4" />,
    },
  ];

  return (
    <nav
      dir="ltr"
      className="border-b backdrop-blur-md sticky top-0 z-50"
      style={{
        backgroundColor: `${theme.colors.surface}E6`,
        borderColor: theme.colors.border,
      }}
      aria-label="Main Navigation"
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Left: Logo */}
          <div className="flex items-center gap-3">
            <Logo />
          </div>

          {/* Center: Navigation Links */}
          <div className="hidden md:flex items-center gap-1">
            {navItems.map((item) => {
              const isActive = location.pathname === item.path;
              return (
                <Link key={item.path} to={item.path}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    size="sm"
                    className="flex items-center gap-2 transition-colors"
                    style={{
                      backgroundColor: isActive
                        ? theme.colors.accent
                        : "transparent",
                      color: isActive
                        ? theme.colors.inverse
                        : theme.colors.headline,
                    }}
                    onMouseEnter={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.backgroundColor =
                          theme.colors.hover;
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.backgroundColor = "transparent";
                      }
                    }}
                  >
                    {item.icon}
                    {item.label}
                  </Button>
                </Link>
              );
            })}
          </div>

          {/* Right: Theme Toggle + Mobile Menu */}
          <div className="flex items-center gap-3">
            <ThemeToggle size="sm" variant="outline" />

            {/* Mobile Navigation */}
            <div className="md:hidden">
              <select
                value={location.pathname}
                onChange={(e) => (window.location.href = e.target.value)}
                className="px-3 py-2 rounded-md border text-sm"
                style={{
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                  color: theme.colors.headline,
                }}
              >
                {navItems.map((item) => (
                  <option key={item.path} value={item.path}>
                    {item.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
