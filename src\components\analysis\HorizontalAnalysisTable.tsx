import React from "react";
import { motion } from "framer-motion";
import {
  <PERSON>,
  Wrench,
  Lightbulb,
  Search,
  TrendingUp,
  DollarSign,
  <PERSON>,
  <PERSON>,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { HorizontalAnalysisResult } from "@/lib/uxAnalyzer";
import { useTheme } from "../../themes";
import { getThemeRiskColor, getThemeScoreColor } from "../../utils/themeUtils";

interface HorizontalAnalysisTableProps {
  result: HorizontalAnalysisResult;
  className?: string;
}

// Table row configuration
interface TableRow {
  key: keyof HorizontalAnalysisResult;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  renderValue?: (value: any) => React.ReactNode;
}

const HorizontalAnalysisTable: React.FC<HorizontalAnalysisTableProps> = ({
  result,
  className = "",
}) => {
  const { theme } = useTheme();

  // Theme-aware helper functions
  const getInnovationLevelStyle = (level: string) =>
    getThemeRiskColor(level, theme);
  const getScoreColorStyle = (score: number) =>
    getThemeScoreColor(score, theme);

  // Theme-aware table configuration
  const getTableRows = (): TableRow[] => [
    {
      key: "targetAudience",
      icon: Users,
      label: "Target Audience",
    },
    {
      key: "problemsSolved",
      icon: Wrench,
      label: "Problems Solved",
    },
    {
      key: "proposedSolution",
      icon: Lightbulb,
      label: "Proposed Solution",
    },
    {
      key: "competitors",
      icon: Search,
      label: "Competitors",
      renderValue: (competitors: string[]) => (
        <div className="flex flex-wrap gap-1">
          {competitors.map((competitor, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {competitor}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: "scalability",
      icon: TrendingUp,
      label: "Scalability",
    },
    {
      key: "revenueModel",
      icon: DollarSign,
      label: "Revenue Model",
    },
    {
      key: "innovationLevel",
      icon: Brain,
      label: "Innovation Level",
      renderValue: (level: "Low" | "Medium" | "High") => (
        <Badge className="font-medium" style={getInnovationLevelStyle(level)}>
          {level}
        </Badge>
      ),
    },
    {
      key: "overallScore",
      icon: Rocket,
      label: "Overall Score",
      renderValue: (score: number) => (
        <div className="flex items-center gap-2">
          <Badge
            className="font-bold text-lg px-3 py-1"
            style={getScoreColorStyle(score)}
          >
            {score}/100
          </Badge>
          <span
            className="text-sm"
            style={{ color: theme.colors.text.secondary }}
          >
            {score >= 80
              ? "Excellent"
              : score >= 60
              ? "Good"
              : "Needs Improvement"}
          </span>
        </div>
      ),
    },
  ];
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const rowVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      className={className}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <Card className="w-full shadow-lg" dir="ltr">
        <CardHeader className="pb-4">
          <CardTitle
            className="text-xl font-bold flex items-center gap-2"
            style={{ color: theme.colors.text.primary }}
          >
            <Brain
              className="w-6 h-6"
              style={{ color: theme.colors.primary[600] }}
            />
            Detailed Analysis Results
          </CardTitle>
        </CardHeader>

        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <tbody>
                {getTableRows().map((row, index) => {
                  const IconComponent = row.icon;
                  const value = result[row.key];

                  return (
                    <motion.tr
                      key={row.key}
                      variants={rowVariants}
                      className="border-b transition-colors duration-200"
                      style={{
                        borderColor: theme.colors.border.primary,
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor =
                          theme.colors.background.secondary;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = "transparent";
                      }}
                    >
                      {/* Label Column */}
                      <td
                        className="py-4 px-4 border-r w-1/3 min-w-[200px]"
                        style={{
                          backgroundColor: theme.colors.background.secondary,
                          borderColor: theme.colors.border.primary,
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <IconComponent
                            className="w-5 h-5 flex-shrink-0"
                            style={{ color: theme.colors.primary[600] }}
                          />
                          <span
                            className="font-semibold text-sm"
                            style={{ color: theme.colors.text.primary }}
                          >
                            {row.label}
                          </span>
                        </div>
                      </td>

                      {/* Value Column */}
                      <td className="py-4 px-4 w-2/3">
                        <div
                          className="leading-relaxed"
                          style={{ color: theme.colors.text.secondary }}
                        >
                          {row.renderValue ? (
                            row.renderValue(value)
                          ) : (
                            <span className="text-sm">{value as string}</span>
                          )}
                        </div>
                      </td>
                    </motion.tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default HorizontalAnalysisTable;
