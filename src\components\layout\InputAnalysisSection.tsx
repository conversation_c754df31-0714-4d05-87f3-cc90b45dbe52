import React from "react";
import {
  IdeaInputF<PERSON>,
  AnalysisButton,
  InputContainer,
  useInputEnhancer,
} from "@/components/input";

interface InputAnalysisSectionProps {
  input: string;
  setInput: (value: string) => void;
  isAnalyzing: boolean;
  handleAnalyze: () => void;
  isEnhancing: boolean;
  handleEnhancePrompt?: () => void; // Optional since it's not used in the refactored version
}

const InputAnalysisSection: React.FC<InputAnalysisSectionProps> = ({
  input,
  setInput,
  isAnalyzing,
  handleAnalyze,
  isEnhancing,
}) => {
  const { handleEnhance } = useInputEnhancer({ setInput });

  return (
    <InputContainer title="Analyze Your SaaS Idea">
      <IdeaInputForm
        input={input}
        setInput={setInput}
        isAnalyzing={isAnalyzing}
        isEnhancing={isEnhancing}
        onEnhance={handleEnhance}
      />
      <AnalysisButton
        onClick={handleAnalyze}
        isAnalyzing={isAnalyzing}
        isEnhancing={isEnhancing}
        input={input}
      />
    </InputContainer>
  );
};

export default InputAnalysisSection;
