import { Theme } from "../themes/types";

/**
 * Utility functions for theme-aware styling
 */

/**
 * Get theme-aware score color based on score value
 */
export const getThemeScoreColor = (score: number, theme: Theme) => {
  if (score >= 80) {
    return {
      backgroundColor: theme.colors.success,
      color: theme.colors.inverse,
    };
  }
  if (score >= 60) {
    return {
      backgroundColor: theme.colors.warning,
      color: theme.colors.inverse,
    };
  }
  if (score >= 40) {
    return {
      backgroundColor: "#f97316", // Orange for average
      color: theme.colors.inverse,
    };
  }
  return {
    backgroundColor: theme.colors.error,
    color: theme.colors.inverse,
  };
};

/**
 * Get theme-aware risk color based on risk level
 */
export const getThemeRiskColor = (level: string, theme: Theme) => {
  switch (level.toLowerCase()) {
    case "low":
      return {
        backgroundColor: theme.colors.success,
        color: theme.colors.inverse,
      };
    case "medium":
      return {
        backgroundColor: theme.colors.warning,
        color: theme.colors.inverse,
      };
    case "high":
      return {
        backgroundColor: theme.colors.error,
        color: theme.colors.inverse,
      };
    default:
      return {
        backgroundColor: theme.colors.secondary[100],
        color: theme.colors.paragraph,
      };
  }
};

/**
 * Get theme-aware status color based on status type
 */
export const getThemeStatusColor = (
  status: "success" | "warning" | "error" | "info",
  theme: Theme
) => {
  const statusColors = {
    success: theme.colors.success,
    warning: theme.colors.warning,
    error: theme.colors.error,
    info: theme.colors.info,
  };

  return {
    backgroundColor: statusColors[status],
    color: theme.colors.inverse,
    borderColor: statusColors[status],
  };
};

/**
 * Get theme-aware validity color based on validity level
 */
export const getThemeValidityColor = (validity: string, theme: Theme) => {
  switch (validity) {
    case "Realistic":
    case "Promising":
      return getThemeStatusColor("success", theme);
    case "Weak":
      return getThemeStatusColor("warning", theme);
    case "High-Risk":
      return getThemeStatusColor("error", theme);
    default:
      return getThemeStatusColor("info", theme);
  }
};

/**
 * Get theme-aware complexity color based on complexity level
 */
export const getThemeComplexityColor = (complexity: string, theme: Theme) => {
  switch (complexity.toLowerCase()) {
    case "low":
    case "simple":
      return getThemeRiskColor("low", theme);
    case "medium":
    case "moderate":
      return getThemeRiskColor("medium", theme);
    case "high":
    case "complex":
    case "very high":
      return getThemeRiskColor("high", theme);
    default:
      return {
        backgroundColor: theme.colors.secondary[100],
        color: theme.colors.paragraph,
      };
  }
};

/**
 * Get theme-aware priority color based on priority level
 */
export const getThemePriorityColor = (priority: string, theme: Theme) => {
  switch (priority.toLowerCase()) {
    case "high":
      return getThemeStatusColor("error", theme);
    case "medium":
      return getThemeStatusColor("warning", theme);
    case "low":
      return getThemeStatusColor("success", theme);
    default:
      return getThemeStatusColor("info", theme);
  }
};

/**
 * Create theme-aware CSS variables object
 */
export const createThemeVariables = (theme: Theme) => {
  return {
    "--theme-bg-primary": theme.colors.background,
    "--theme-bg-secondary": theme.colors.hover,
    "--theme-bg-card": theme.colors.surface,
    "--theme-text-primary": theme.colors.headline,
    "--theme-text-secondary": theme.colors.paragraph,
    "--theme-border-primary": theme.colors.border,
    "--theme-table-header-bg": theme.colors.hover,
    "--theme-table-row-bg": theme.colors.surface,
    "--theme-table-row-hover": theme.colors.hover,
  };
};

/**
 * Apply theme-aware hover styles
 */
export const getThemeHoverStyles = (theme: Theme) => {
  return {
    "&:hover": {
      backgroundColor: theme.colors.hover,
    },
  };
};
