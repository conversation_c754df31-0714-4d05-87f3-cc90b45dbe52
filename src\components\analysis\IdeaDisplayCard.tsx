import React, { useState } from "react";
import { motion } from "framer-motion";
import { Target, Calendar, Clock, Eye, EyeOff } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useTheme } from "../../themes";
import { getThemeScoreColor } from "../../utils/themeUtils";

interface IdeaDisplayCardProps {
  originalIdea: string;
  timestamp: string;
  overallScore: number;
  className?: string;
}

const IdeaDisplayCard: React.FC<IdeaDisplayCardProps> = ({
  originalIdea,
  timestamp,
  overallScore,
  className = "",
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { theme } = useTheme();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const getScoreColorStyle = (score: number) =>
    getThemeScoreColor(score, theme);

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Excellent";
    if (score >= 60) return "Good";
    return "Needs Improvement";
  };

  // Truncate text to exactly 180 characters
  const truncateText = (text: string) => {
    if (text.length <= 180) return text;
    return text.substring(0, 180) + "...";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      <Card
        className="w-full shadow-lg border-l-4"
        dir="ltr"
        style={{ borderLeftColor: theme.colors.primary[500] }}
      >
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle
              className="text-xl font-bold flex items-center gap-2"
              style={{ color: theme.colors.headline }}
            >
              <Target
                className="w-6 h-6"
                style={{ color: theme.colors.accent }}
              />
              Original Idea
            </CardTitle>
            <Badge
              className="font-bold px-3 py-1"
              style={getScoreColorStyle(overallScore)}
            >
              {overallScore}/100 - {getScoreLabel(overallScore)}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Original Idea Text with Expandable Preview */}
          <div
            className="rounded-lg p-4 border-l-2"
            style={{
              backgroundColor: theme.colors.hover,
              borderLeftColor: theme.colors.accent,
            }}
          >
            <p
              className="leading-relaxed text-base font-medium"
              style={{ color: theme.colors.headline }}
            >
              {isExpanded ? originalIdea : truncateText(originalIdea)}
            </p>

            {/* Toggle Button - Only show when text exceeds 180 characters */}
            {originalIdea.length > 180 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="mt-2 p-0 h-auto"
                style={{ color: theme.colors.primary[600] }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = theme.colors.primary[800];
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = theme.colors.primary[600];
                }}
              >
                {isExpanded ? (
                  <>
                    <EyeOff className="w-3 h-3 mr-1" />
                    Read less
                  </>
                ) : (
                  <>
                    <Eye className="w-3 h-3 mr-1" />
                    Read more
                  </>
                )}
              </Button>
            )}
          </div>

          {/* Metadata */}
          <div
            className="flex items-center justify-between text-sm pt-2 border-t"
            style={{
              color: theme.colors.text.secondary,
              borderColor: theme.colors.border.primary,
            }}
          >
            <div className="flex items-center gap-2">
              <Calendar
                className="w-4 h-4"
                style={{ color: theme.colors.text.secondary }}
              />
              <span>Analysis Date:</span>
              <span
                className="font-medium"
                style={{ color: theme.colors.text.primary }}
              >
                {formatDate(timestamp)}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Clock
                className="w-4 h-4"
                style={{ color: theme.colors.text.secondary }}
              />
              <span>Analysis Completed</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default IdeaDisplayCard;
